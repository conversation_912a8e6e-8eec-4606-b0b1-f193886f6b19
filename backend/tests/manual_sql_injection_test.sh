#!/bin/bash

# Manual SQL Injection Test Script for Login Endpoint
# This script tests various SQL injection payloads against the login endpoint

echo "=== SQL Injection Testing for Login Endpoint ==="
echo "Starting server in background..."

# Start the server in background (assuming it runs on port 8080)
# Uncomment the next line if you want to start the server automatically
# go run ../cmd/main.go &
# SERVER_PID=$!
# sleep 3

BASE_URL="http://localhost:8080"
LOGIN_ENDPOINT="$BASE_URL/login"

echo "Testing endpoint: $LOGIN_ENDPOINT"
echo ""

# Test 1: Basic SQL injection in email field
echo "Test 1: Basic SQL injection in email field"
echo "Payload: ' OR '1'='1"
curl -s -X POST "$LOGIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"email": "'\'' OR '\''1'\''='\''1", "password": "TestPassword123!"}' \
  | jq '.' || echo "Response not JSON"
echo ""

# Test 2: Union-based injection
echo "Test 2: Union-based injection"
echo "Payload: ' UNION SELECT 1,'admin','password123'--"
curl -s -X POST "$LOGIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>'\'' UNION SELECT 1,'\''admin'\'','\''password123'\''--", "password": "TestPassword123!"}' \
  | jq '.' || echo "Response not JSON"
echo ""

# Test 3: Stacked queries (dangerous)
echo "Test 3: Stacked queries (dangerous)"
echo "Payload: <EMAIL>'; DROP TABLE Users;--"
curl -s -X POST "$LOGIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>'\''; DROP TABLE Users;--", "password": "TestPassword123!"}' \
  | jq '.' || echo "Response not JSON"
echo ""

# Test 4: SQL injection in password field
echo "Test 4: SQL injection in password field"
echo "Payload: ' OR 1=1--"
curl -s -X POST "$LOGIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "'\'' OR 1=1--"}' \
  | jq '.' || echo "Response not JSON"
echo ""

# Test 5: Form data injection
echo "Test 5: Form data injection"
echo "Payload: ' OR '1'='1 (via form data)"
curl -s -X POST "$LOGIN_ENDPOINT" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=' OR '1'='1&password=TestPassword123!" \
  | jq '.' || echo "Response not JSON"
echo ""

# Test 6: Valid login (should work)
echo "Test 6: Valid login (should work if user exists)"
echo "Credentials: <EMAIL> / TestPassword123!"
curl -s -X POST "$LOGIN_ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPassword123!"}' \
  | jq '.' || echo "Response not JSON"
echo ""

echo "=== Testing Complete ==="
echo ""
echo "Expected Results:"
echo "- Tests 1-5 should FAIL (return 401 Unauthorized or similar)"
echo "- Test 6 should SUCCEED only if the test user exists"
echo "- No SQL injection should succeed"
echo "- Database should remain intact"

# Cleanup
# if [ ! -z "$SERVER_PID" ]; then
#   echo "Stopping server..."
#   kill $SERVER_PID
# fi
